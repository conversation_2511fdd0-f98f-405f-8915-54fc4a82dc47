"""Defines the shift instance endpoint."""

import logging
import os

from aws_lambda_powertools import Tracer
from fastapi import APIRouter, Depends, Query, Request
from lib_public_api_utilities.dependencies.timing import EpochTimestamp
from lib_public_api_utilities.error_handling.gravitee_error_handler import (
    GraviteeHTTPException,
)
from lib_public_api_utilities.error_handling.http_error_responses import (
    HTTP_500_INTERNAL,
)
from lib_public_api_utilities.shifts.db_client import ShiftDBClient
from lib_public_api_utilities.shifts.equipment import validate_line_id
from lib_public_api_utilities.shifts.timing import time_conversion_and_validation

from common.constants import (
    ACCOUNT_DESCRIPTION,
    LINE_ID_DESCRIPTION,
)
from common.utils import generate_pk, extract_qualification_ids
from models.instances_models import InstanceResponse

LOGGER = logging.getLogger(__name__)
LOGGER.setLevel(os.getenv("LOG_LEVEL") or logging.INFO)

router = APIRouter()

TRACER = Tracer("service=shift-service-public-api/instances")

HOURS_24 = ********


@router.get(
    "/instances",
    dependencies=[Depends(validate_line_id)],
    response_model=InstanceResponse,
    response_model_by_alias=True,
)
@TRACER.capture_method(capture_response=False)
def shift_instances(
    request: Request,
    line_id: str = Query(..., description=LINE_ID_DESCRIPTION),
    dates: EpochTimestamp = Depends(time_conversion_and_validation),
    account: str = Query(..., description=ACCOUNT_DESCRIPTION),
) -> InstanceResponse:
    """Fetch instances.

    Obtain information about all instances for a specific line
    that occurred within a specified timeframe.
    The response returns all shift instances whose start or end
    times fall within the queried time interval [start, end].
    """
    start, end = dates.start, dates.end
    LOGGER.info(
        'Calling shift-instances endpoint with event: "%s".',
        request.scope.get("aws.event"),
    )

    try:
        dynamodb_client = ShiftDBClient()
        pk = generate_pk(account, line_id)
        # lets calculate from the 24 hr before
        # as max shift can be of 24 hr
        # feature request E18ANA-6494
        new_start = start - HOURS_24
        count = 0
        instances = dynamodb_client.get_instances_in_range(pk, str(new_start), str(end))
        qualification_ids = extract_qualification_ids(instances)
        if qualification_ids is not None:
            get_qualification_data(account: str, qualification_ids: list[str]) -> dict
        for instance in instances:
        if instance.start >= start or (instance.start <= start and instance.end >= start):
                    break
            count += 1
        return InstanceResponse(root=instances[count:])

    except GraviteeHTTPException as http_excep:
        LOGGER.exception("shift-instances failed")
        raise http_excep
    # overwrite possible internal error messages
    except Exception as excep:
        LOGGER.exception("shift-instances failed")
        raise GraviteeHTTPException(status_code=500, message=HTTP_500_INTERNAL) from excep
